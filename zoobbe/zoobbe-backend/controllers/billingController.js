const Subscription = require('../models/Subscription');
const Workspace = require('../models/Workspace');
const User = require('../models/User');
const { SUBSCRIPTION_LIMITS } = require('../utils/helper');
// const { SUBSCRIPTION_LIMITS } = require('../utils/constants');

/**
 * Get billing status for a workspace
 * Returns information about seats, pending payments, and billing cycle
 */
const getBillingStatus = async (req, res) => {
    try {
        const { workspaceId } = req.params;
        const userId = req.user.id;

        // Find workspace and verify user access
        const workspace = await Workspace.findOne({ shortId: workspaceId })
            .populate('subscription')
            .populate('members.user', 'name email')
            .populate('guests', 'name email');

        if (!workspace) {
            return res.status(404).json({ message: 'Workspace not found' });
        }

        // Check if user is admin
        const isAdmin = workspace.ownerId === userId ||
            workspace.members.some(member =>
                member.user._id.toString() === userId && member.role === 'admin'
            );

        if (!isAdmin) {
            return res.status(403).json({ message: 'Only admins can view billing status' });
        }

        // Check for multiple subscriptions (debugging)
        const allSubscriptions = await Subscription.find({ workspaceId: workspace._id });
        console.log(`Found ${allSubscriptions.length} subscriptions for workspace ${workspaceId}:`,
            allSubscriptions.map(sub => ({
                id: sub._id,
                reservedSeats: sub.reservedSeats,
                status: sub.status,
                createdAt: sub.createdAt
            }))
        );

        const subscription = workspace.subscription;

        if (!subscription) {
            return res.json({
                isPro: false,
                planType: 'Free',
                totalMembers: workspace.members.length + workspace.guests.length,
                memberLimit: SUBSCRIPTION_LIMITS['Free']
            });
        }

        const isPro = ['Standard', 'Premium', 'Enterprise'].includes(subscription.planType) &&
                     subscription.status === 'active';

        const totalMembers = workspace.members.length + workspace.guests.length;
        const reservedSeats = subscription.reservedSeats || 0; // How many seats are already paid for
        const pendingSeats = subscription.pendingSeats || 0; // How many seats need payment

        // Calculate members with different access levels
        const fullAccessMembers = workspace.members.filter(m => m.accessLevel === 'full').length;
        const viewOnlyMembers = workspace.members.filter(m => m.accessLevel === 'view-only').length;
        const limitedAccessMembers = workspace.members.filter(m => m.accessLevel === 'limited').length;

        // Calculate next billing date
        const nextBillingDate = subscription.currentPeriodEnd;
        const daysUntilBilling = Math.ceil((nextBillingDate - new Date()) / (1000 * 60 * 60 * 24));

        // Calculate how many seats are actually being used vs paid for
        const seatsInUse = totalMembers; // Current members in workspace
        const paidSeats = reservedSeats; // Seats already paid for
        const unpaidMembers = Math.max(0, seatsInUse - paidSeats); // Members without paid seats

        // Debug logging
        console.log(`Billing Status Debug for workspace ${workspace.shortId}:`, {
            totalMembers,
            seatsInUse,
            reservedSeats,
            paidSeats,
            unpaidMembers,
            pendingSeats,
            subscriptionId: subscription._id,
            subscriptionWorkspaceId: subscription.workspaceId,
            actualWorkspaceId: workspace._id,
            subscriptionObject: {
                reservedSeats: subscription.reservedSeats,
                pendingSeats: subscription.pendingSeats,
                quantity: subscription.quantity,
                status: subscription.status
            }
        });

        res.json({
            isPro,
            planType: subscription.planType,
            billingCycle: subscription.billingCycle,
            status: subscription.status,

            // Member counts
            totalMembers,
            seatsInUse,

            // Seat status
            paidSeats, // How many seats are already paid for (reserved)
            unpaidMembers, // How many current members don't have paid seats
            pendingSeats, // Additional seats that need payment
            availableSeats: Math.max(0, paidSeats - seatsInUse), // Paid seats not currently used

            // Legacy fields for compatibility
            reservedSeats,
            totalSeats: paidSeats + pendingSeats,

            accessLevels: {
                full: fullAccessMembers,
                viewOnly: viewOnlyMembers,
                limited: limitedAccessMembers
            },
            billing: {
                nextBillingDate,
                daysUntilBilling,
                currentPeriodStart: subscription.currentPeriodStart,
                currentPeriodEnd: subscription.currentPeriodEnd
            },
            seatHistory: subscription.seatHistory || []
        });

    } catch (error) {
        console.error('Error getting billing status:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

/**
 * Add pending seats when new members are added
 */
const addPendingSeats = async (workspaceId, memberCount, userId, reason = 'Member added') => {
    try {
        const subscription = await Subscription.findOne({ workspaceId });

        if (!subscription) {
            return null;
        }

        subscription.pendingSeats = (subscription.pendingSeats || 0) + memberCount;
        subscription.totalSeats = (subscription.reservedSeats || 0) + (subscription.pendingSeats || 0);

        // Add to seat history
        subscription.seatHistory.push({
            action: 'added',
            seats: memberCount,
            userId,
            reason
        });

        await subscription.save();
        return subscription;

    } catch (error) {
        console.error('Error adding pending seats:', error);
        throw error;
    }
};

/**
 * Calculate prorated amount for mid-cycle seat additions
 */
const calculateProratedAmount = (planType, billingCycle, seats, currentPeriodStart, currentPeriodEnd) => {
    const planPricing = {
        'Standard': { monthly: 4.99, annually: 49.99 },
        'Premium': { monthly: 9.99, annually: 95.99 }
    };

    const basePrice = planPricing[planType]?.[billingCycle] || 0;
    const totalDays = Math.ceil((currentPeriodEnd - currentPeriodStart) / (1000 * 60 * 60 * 24));
    const remainingDays = Math.ceil((currentPeriodEnd - new Date()) / (1000 * 60 * 60 * 24));

    const proratedRatio = remainingDays / totalDays;
    const proratedAmount = Math.round(basePrice * seats * proratedRatio * 100); // in cents

    return {
        basePrice,
        totalDays,
        remainingDays,
        proratedRatio,
        proratedAmount,
        proratedAmountDollars: proratedAmount / 100
    };
};

/**
 * Reserve seats within the same billing cycle when members are removed
 */
const reserveSeats = async (workspaceId, memberCount, userId, reason = 'Member removed') => {
    try {
        const subscription = await Subscription.findOne({ workspaceId });

        if (!subscription) {
            return null;
        }

        // Move seats from reserved to available for reuse
        subscription.seatHistory.push({
            action: 'reserved',
            seats: memberCount,
            userId,
            reason
        });

        await subscription.save();
        return subscription;

    } catch (error) {
        console.error('Error reserving seats:', error);
        throw error;
    }
};

module.exports = {
    getBillingStatus,
    addPendingSeats,
    calculateProratedAmount,
    reserveSeats
};
