const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/authMiddleware');
const allowPaymentAction = require('../middleware/workspaceAuthMiddleware');
const { getBillingStatus } = require('../controllers/billingController');
const Subscription = require('../models/Subscription');
const Workspace = require('../models/Workspace');
const Stripe = require('stripe');
const { calculateProratedAmount } = require('../controllers/billingController');

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Get billing status for a workspace
router.get('/workspace/:workspaceId/status', authenticateToken, getBillingStatus);

// Pay for pending seats
router.post('/workspace/:workspaceId/pay-pending-seats', 
    authenticateToken, 
    async (req, res) => {
        try {
            const { workspaceId } = req.params;
            const userId = req.user.id;

            // Find workspace and verify admin access
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ message: 'Workspace not found' });
            }

            const isAdmin = workspace.ownerId === userId || 
                workspace.members.some(member => 
                    member.user._id.toString() === userId && member.role === 'admin'
                );

            if (!isAdmin) {
                return res.status(403).json({ message: 'Only admins can pay for seats' });
            }

            const subscription = workspace.subscription;
            
            if (!subscription || subscription.pendingSeats <= 0) {
                return res.status(400).json({ message: 'No pending seats to pay for' });
            }

            // Calculate prorated amount
            const proratedData = calculateProratedAmount(
                subscription.planType,
                subscription.billingCycle,
                subscription.pendingSeats,
                subscription.currentPeriodStart,
                subscription.currentPeriodEnd
            );

            // Create payment intent for pending seats
            const paymentIntent = await stripe.paymentIntents.create({
                amount: proratedData.proratedAmount,
                currency: 'usd',
                customer: subscription.stripeCustomerId,
                metadata: {
                    workspaceId: workspace._id.toString(),
                    userId,
                    planType: subscription.planType,
                    billingCycle: subscription.billingCycle,
                    pendingSeats: subscription.pendingSeats.toString(),
                    type: 'pending_seats_payment'
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            res.json({
                clientSecret: paymentIntent.client_secret,
                amount: proratedData.proratedAmount,
                amountDollars: proratedData.proratedAmountDollars,
                seats: subscription.pendingSeats,
                proratedData
            });

        } catch (error) {
            console.error('Error creating payment for pending seats:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

// Increase reserved seats (for future members)
router.post('/workspace/:workspaceId/increase-seats',
    authenticateToken,
    async (req, res) => {
        try {
            const { workspaceId } = req.params;
            const { additionalSeats } = req.body;
            const userId = req.user.id;

            if (!additionalSeats || additionalSeats <= 0) {
                return res.status(400).json({ message: 'Additional seats must be a positive number' });
            }

            // Find workspace and verify admin access
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ message: 'Workspace not found' });
            }

            const isAdmin = workspace.ownerId === userId || 
                workspace.members.some(member => 
                    member.user._id.toString() === userId && member.role === 'admin'
                );

            if (!isAdmin) {
                return res.status(403).json({ message: 'Only admins can increase seats' });
            }

            const subscription = workspace.subscription;
            
            if (!subscription) {
                return res.status(400).json({ message: 'No active subscription found' });
            }

            // Calculate prorated amount for additional seats
            const proratedData = calculateProratedAmount(
                subscription.planType,
                subscription.billingCycle,
                additionalSeats,
                subscription.currentPeriodStart,
                subscription.currentPeriodEnd
            );

            // Create payment intent for additional seats
            const paymentIntent = await stripe.paymentIntents.create({
                amount: proratedData.proratedAmount,
                currency: 'usd',
                customer: subscription.stripeCustomerId,
                metadata: {
                    workspaceId: workspace._id.toString(),
                    userId,
                    planType: subscription.planType,
                    billingCycle: subscription.billingCycle,
                    additionalSeats: additionalSeats.toString(),
                    type: 'additional_seats_payment'
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            res.json({
                clientSecret: paymentIntent.client_secret,
                amount: proratedData.proratedAmount,
                amountDollars: proratedData.proratedAmountDollars,
                seats: additionalSeats,
                proratedData
            });

        } catch (error) {
            console.error('Error creating payment for additional seats:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

module.exports = router;
