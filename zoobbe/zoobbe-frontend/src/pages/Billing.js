// src/components/Billing.js

import { useDispatch, useSelector } from "react-redux";
import { config } from "../config";
import { stripePromise } from "../utils/stripe";

import './scss/BillingTable.scss';
import { useEffect, useState } from "react";
import Checkout from "./Checkout";
import { fetchWorkspaces } from "../redux/Slices/workspaceSlice";
import { useNavigate, useParams } from "react-router-dom";
import { find } from "../utils/helpers";
import { fetchMembers } from "../redux/Slices/memberSlice";

const plans = [
    {
        name: 'Free',
        description: 'Perfect for individuals and small teams getting started with Zoobbe.',
        prices: { monthly: '$0 USD', annually: '$0 USD' },
        subTexts: { monthly: 'Free forever', annually: 'Free forever' },
        features: [
            'Unlimited cards',
            'Boards: Up to 15',
            'Collaborators: 15',
            'File attachments: 20 MB per file',
            'Storage: 512 MB per workspace',
            'Basic checklists',
            'Mentions & real-time notifications',
            'Custom backgrounds & stickers',
            'Last activity status for all members'
        ]
    },
    {
        name: 'Standard',
        description: 'More power for growing teams: organize, customize, and collaborate better.',
        prices: { monthly: '$4.99 USD', annually: '$49.99 USD' },
        pricesId: { monthly: 'price_1RHOgxK0cSs6yAeTY4mwEMMZ', annually: 'price_1RHOgxK0cSs6yAeT38dhronu' },
        subTexts: { monthly: 'per seat / month', annually: 'per seat / year' },
        cta: 'Upgrade now',
        features: [
            'Unlimited cards',
            'Boards: Unlimited',
            'Collaborators: Unlimited',
            'File attachments: 250 MB per file',
            'Storage: Unlimited',
            'Saved searches',
            'Advanced checklists',
            'List colors',
            'Board backgrounds & stickers',
            'Last activity status for all members',
            'Online status visibility (if member of any Pro workspace)',
            'Pro verified badge for Pro workspace members'
        ]
    },
    {
        name: 'Premium',
        description: 'For teams that need insights, controls, and more collaboration power.',
        prices: { monthly: '$9.99 USD', annually: '$95.99 USD' },
        pricesId: { monthly: 'price_1RHOkkK0cSs6yAeTFtoiFIZi', annually: 'price_1RHOkkK0cSs6yAeT3u6MgEM6' },
        subTexts: { monthly: 'per seat / month', annually: 'per seat / year' },
        cta: 'Upgrade now',
        features: [
            'Unlimited cards',
            'Boards: Unlimited',
            'Collaborators: Unlimited',
            'File attachments: 250 MB per file',
            'Storage: Unlimited',
            'Saved searches',
            'Advanced checklists',
            'List colors',
            'Board backgrounds & stickers',
            'Custom stickers & emojis',
            'Data export (JSON/CSV)',
            'Extended activity logs',
            'Priority support',
            'Last activity status for all members',
            'Online status visibility (if member of any Pro workspace)',
            'Pro verified badge for Pro workspace members'
        ]
    },
    {
        name: 'Enterprise',
        description: 'Enterprise-grade security, support, and scale. Tailored for large organizations.',
        prices: {
            monthly: 'Contact sales',
            annually: 'Billing varies with number of users'
        },
        subTexts: { monthly: '', annually: '' },
        cta: 'Contact sales',
        features: [
            'Unlimited cards',
            'Boards: Unlimited',
            'Collaborators: Unlimited',
            'File attachments: 250 MB per file',
            'Storage: Unlimited',
            'Saved searches',
            'Advanced checklists',
            'List colors',
            'Board backgrounds & stickers',
            'Custom stickers & emojis',
            'Data export (JSON/CSV)',
            'Extended activity logs',
            'Priority support',
            'Dedicated support contact',
            'Attachment restrictions',
            'User role management',
            'Organization-wide permissions',
            'Public board visibility controls',
            'SSO (Coming Soon)',
            'Custom security policies',
            'Last activity status for all members',
            'Online status visibility (if member of any Pro workspace)',
            'Pro verified badge for Pro workspace members'
        ]
    }
];



function Billing() {

    const { user } = useSelector((state) => state.user);
    const [isYearly, setIsYearly] = useState(true);
    const [showCheckout, setShowCheckout] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState(null);

    const { shortId } = useParams();
    const dispatch = useDispatch();


    const members = useSelector(state => state.member.workspaceMembers);
    const guests = useSelector(state => state.member.workspaceGuests);

    const userCount = members?.length + guests?.length;

    const navigate = useNavigate();

    useEffect(() => {
        dispatch(fetchMembers({ type: 'workspace', id: shortId }));
        dispatch(fetchMembers({ type: 'guests', id: shortId }));
    }, [dispatch, shortId]);


    const handleSubscribe = async (planId) => {
        const stripe = await stripePromise;

        const res = await fetch(config.API_URI + '/api/stripe/create-checkout-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ planId, userId: user.user._id }) // userId from auth
        });

        const data = await res.json();

        if (data.url) {
            window.location.href = data.url; // Redirect to Stripe Checkout
        } else {
            alert("Something went wrong.");
        }
    };

    const [billing, setBilling] = useState('annually');

    const handleCTAClick = (plan) => {
        const selectedPriceId = plan.pricesId[billing]; // Get the correct price ID based on billing cycle
        setShowCheckout(true);
        setSelectedPlan({
            ...plan,
            priceId: selectedPriceId // Add the price ID to the plan object
        });
    };
    const handleNavigate = () => {
        navigate('/');
    };



    return (
        <>

            {
                !showCheckout && (
                    <div className="billing-container">
                        <div className="close-button" onClick={() => handleNavigate()}>
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e3e3e3"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" /></svg>
                        </div>

                        <p className="trial-note">Your Premium free trial ended on August 14, 2024.</p>

                        <div className="billing-toggle">
                            <button className={billing === 'monthly' ? 'active' : ''} onClick={() => setBilling('monthly')}>Monthly</button>
                            <button className={billing === 'annually' ? 'active' : ''} onClick={() => setBilling('annually')}>
                                Annually <span className="discount">SAVE 17%</span>
                            </button>
                        </div>

                        <div className="billing-cards">
                            {plans.map((plan) => (
                                <div className={`card ${plan.name.toLowerCase()}`} key={plan.name}>
                                    <h2>{plan.name}</h2>
                                    <div className="description">{plan.description}</div>
                                    <div className="price">{plan.prices && plan.prices[billing]}</div>
                                    <div className="sub-text">{plan.subTexts && plan.subTexts[billing]}</div>
                                    {plan.cta && <button className="cta-button" onClick={(e) => handleCTAClick(plan)}><span>{plan.cta}</span></button>}
                                    <ul className="features">
                                        {plan.features.map((feature, idx) => (
                                            <li key={idx}>
                                                <span className="check">
                                                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#a855f7"><path d="M382-240 154-468l57-57 171 171 367-367 57 57-424 424Z" /></svg>
                                                </span>
                                                <span className="feature-text">{feature}</span></li>
                                        ))}
                                    </ul>
                                </div>
                            ))}
                        </div>
                    </div>
                )
            }


            {
                showCheckout && (
                    <div className="checkout-page">
                        <div className="close-button" onClick={() => setShowCheckout(false)}>
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#e3e3e3"><path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z" /></svg>
                        </div>
                        <Checkout plan={selectedPlan} billing={billing} userCount={userCount} />
                    </div>
                )
            }
        </>
    );
}

export default Billing;
