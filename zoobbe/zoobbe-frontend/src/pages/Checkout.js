// components/Checkout.jsx
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { fetchWorkspaces } from '../redux/Slices/workspaceSlice';
import { find } from '../utils/helpers';
import SearchSelect from '../components/Global/SearchSelect';

import { CardElement, useStripe, useElements, Elements, CardCvcElement, CardExpiryElement, CardNumberElement } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

import './scss/Checkout.scss';
import { config } from '../config';


const stripePromise = loadStripe(config.STRIPE_PUBLIC_KEY); // Replace with your Stripe publishable key

const planPricing = {
    Standard: {
        monthly: 4.99,
        annually: 49.99,
    },
    Premium: {
        monthly: 9.99,
        annually: 95.99,
    },
};

const TX_TAX_RATE = 0.0825; // 8.25% Texas sales tax

const countryOptions = [
    { index: 0, value: 'AF', label: 'Afghanistan' },
    { index: 1, value: 'AL', label: 'Albania' },
    { index: 2, value: 'DZ', label: 'Algeria' },
    { index: 3, value: 'AD', label: 'Andorra' },
    { index: 4, value: 'AO', label: 'Angola' },
    { index: 5, value: 'AG', label: 'Antigua and Barbuda' },
    { index: 6, value: 'AR', label: 'Argentina' },
    { index: 7, value: 'AM', label: 'Armenia' },
    { index: 8, value: 'AU', label: 'Australia' },
    { index: 9, value: 'AT', label: 'Austria' },
    { index: 10, value: 'AZ', label: 'Azerbaijan' },
    { index: 11, value: 'BS', label: 'Bahamas' },
    { index: 12, value: 'BH', label: 'Bahrain' },
    { index: 13, value: 'BD', label: 'Bangladesh' },
    { index: 14, value: 'BB', label: 'Barbados' },
    { index: 15, value: 'BY', label: 'Belarus' },
    { index: 16, value: 'BE', label: 'Belgium' },
    { index: 17, value: 'BZ', label: 'Belize' },
    { index: 18, value: 'BJ', label: 'Benin' },
    { index: 19, value: 'BT', label: 'Bhutan' },
    { index: 20, value: 'BO', label: 'Bolivia' },
    { index: 21, value: 'BA', label: 'Bosnia and Herzegovina' },
    { index: 22, value: 'BW', label: 'Botswana' },
    { index: 23, value: 'BR', label: 'Brazil' },
    { index: 24, value: 'BN', label: 'Brunei' },
    { index: 25, value: 'BG', label: 'Bulgaria' },
    { index: 26, value: 'BF', label: 'Burkina Faso' },
    { index: 27, value: 'BI', label: 'Burundi' },
    { index: 28, value: 'KH', label: 'Cambodia' },
    { index: 29, value: 'CM', label: 'Cameroon' },
    { index: 30, value: 'CA', label: 'Canada' },
    { index: 31, value: 'CV', label: 'Cape Verde' },
    { index: 32, value: 'CF', label: 'Central African Republic' },
    { index: 33, value: 'TD', label: 'Chad' },
    { index: 34, value: 'CL', label: 'Chile' },
    { index: 35, value: 'CN', label: 'China' },
    { index: 36, value: 'CO', label: 'Colombia' },
    { index: 37, value: 'KM', label: 'Comoros' },
    { index: 38, value: 'CG', label: 'Congo (Brazzaville)' },
    { index: 39, value: 'CD', label: 'Congo (Kinshasa)' },
    { index: 40, value: 'CR', label: 'Costa Rica' },
    { index: 41, value: 'HR', label: 'Croatia' },
    { index: 42, value: 'CU', label: 'Cuba' },
    { index: 43, value: 'CY', label: 'Cyprus' },
    { index: 44, value: 'CZ', label: 'Czech Republic' },
    { index: 45, value: 'DK', label: 'Denmark' },
    { index: 46, value: 'DJ', label: 'Djibouti' },
    { index: 47, value: 'DM', label: 'Dominica' },
    { index: 48, value: 'DO', label: 'Dominican Republic' },
    { index: 49, value: 'EC', label: 'Ecuador' },
    { index: 50, value: 'EG', label: 'Egypt' },
    { index: 51, value: 'SV', label: 'El Salvador' },
    { index: 52, value: 'GQ', label: 'Equatorial Guinea' },
    { index: 53, value: 'ER', label: 'Eritrea' },
    { index: 54, value: 'EE', label: 'Estonia' },
    { index: 55, value: 'SZ', label: 'Eswatini' },
    { index: 56, value: 'ET', label: 'Ethiopia' },
    { index: 57, value: 'FJ', label: 'Fiji' },
    { index: 58, value: 'FI', label: 'Finland' },
    { index: 59, value: 'FR', label: 'France' },
    { index: 60, value: 'GA', label: 'Gabon' },
    { index: 61, value: 'GM', label: 'Gambia' },
    { index: 62, value: 'GE', label: 'Georgia' },
    { index: 63, value: 'DE', label: 'Germany' },
    { index: 64, value: 'GH', label: 'Ghana' },
    { index: 65, value: 'GR', label: 'Greece' },
    { index: 66, value: 'GD', label: 'Grenada' },
    { index: 67, value: 'GT', label: 'Guatemala' },
    { index: 68, value: 'GN', label: 'Guinea' },
    { index: 69, value: 'GW', label: 'Guinea-Bissau' },
    { index: 70, value: 'GY', label: 'Guyana' },
    { index: 71, value: 'HT', label: 'Haiti' },
    { index: 72, value: 'HN', label: 'Honduras' },
    { index: 73, value: 'HU', label: 'Hungary' },
    { index: 74, value: 'IS', label: 'Iceland' },
    { index: 75, value: 'IN', label: 'India' },
    { index: 76, value: 'ID', label: 'Indonesia' },
    { index: 77, value: 'IR', label: 'Iran' },
    { index: 78, value: 'IQ', label: 'Iraq' },
    { index: 79, value: 'IE', label: 'Ireland' },
    { index: 80, value: 'IL', label: 'Israel' },
    { index: 81, value: 'IT', label: 'Italy' },
    { index: 82, value: 'JM', label: 'Jamaica' },
    { index: 83, value: 'JP', label: 'Japan' },
    { index: 84, value: 'JO', label: 'Jordan' },
    { index: 85, value: 'KZ', label: 'Kazakhstan' },
    { index: 86, value: 'KE', label: 'Kenya' },
    { index: 87, value: 'KI', label: 'Kiribati' },
    { index: 88, value: 'KR', label: 'Korea, South' },
    { index: 89, value: 'KW', label: 'Kuwait' },
    { index: 90, value: 'KG', label: 'Kyrgyzstan' },
    { index: 91, value: 'LA', label: 'Laos' },
    { index: 92, value: 'LV', label: 'Latvia' },
    { index: 93, value: 'LB', label: 'Lebanon' },
    { index: 94, value: 'LS', label: 'Lesotho' },
    { index: 95, value: 'LR', label: 'Liberia' },
    { index: 96, value: 'LY', label: 'Libya' },
    { index: 97, value: 'LI', label: 'Liechtenstein' },
    { index: 98, value: 'LT', label: 'Lithuania' },
    { index: 99, value: 'LU', label: 'Luxembourg' },
    { index: 100, value: 'MG', label: 'Madagascar' },
    { index: 101, value: 'MW', label: 'Malawi' },
    { index: 102, value: 'MY', label: 'Malaysia' },
    { index: 103, value: 'MV', label: 'Maldives' },
    { index: 104, value: 'ML', label: 'Mali' },
    { index: 105, value: 'MT', label: 'Malta' },
    { index: 106, value: 'MH', label: 'Marshall Islands' },
    { index: 107, value: 'MR', label: 'Mauritania' },
    { index: 108, value: 'MU', label: 'Mauritius' },
    { index: 109, value: 'MX', label: 'Mexico' },
    { index: 110, value: 'FM', label: 'Micronesia' },
    { index: 111, value: 'MD', label: 'Moldova' },
    { index: 112, value: 'MC', label: 'Monaco' },
    { index: 113, value: 'MN', label: 'Mongolia' },
    { index: 114, value: 'ME', label: 'Montenegro' },
    { index: 115, value: 'MA', label: 'Morocco' },
    { index: 116, value: 'MZ', label: 'Mozambique' },
    { index: 117, value: 'MM', label: 'Myanmar' },
    { index: 118, value: 'NA', label: 'Namibia' },
    { index: 119, value: 'NR', label: 'Nauru' },
    { index: 120, value: 'NP', label: 'Nepal' },
    { index: 121, value: 'NL', label: 'Netherlands' },
    { index: 122, value: 'NZ', label: 'New Zealand' },
    { index: 123, value: 'NI', label: 'Nicaragua' },
    { index: 124, value: 'NE', label: 'Niger' },
    { index: 125, value: 'NG', label: 'Nigeria' },
    { index: 126, value: 'NO', label: 'Norway' },
    { index: 127, value: 'OM', label: 'Oman' },
    { index: 128, value: 'PK', label: 'Pakistan' },
    { index: 129, value: 'PW', label: 'Palau' },
    { index: 130, value: 'PA', label: 'Panama' },
    { index: 131, value: 'PG', label: 'Papua New Guinea' },
    { index: 132, value: 'PY', label: 'Paraguay' },
    { index: 133, value: 'PE', label: 'Peru' },
    { index: 134, value: 'PH', label: 'Philippines' },
    { index: 135, value: 'PL', label: 'Poland' },
    { index: 136, value: 'PT', label: 'Portugal' },
    { index: 137, value: 'QA', label: 'Qatar' },
    { index: 138, value: 'RO', label: 'Romania' },
    { index: 139, value: 'RU', label: 'Russia' },
    { index: 140, value: 'RW', label: 'Rwanda' },
    { index: 141, value: 'KN', label: 'Saint Kitts and Nevis' },
    { index: 142, value: 'LC', label: 'Saint Lucia' },
    { index: 143, value: 'VC', label: 'Saint Vincent and the Grenadines' },
    { index: 144, value: 'WS', label: 'Samoa' },
    { index: 145, value: 'SM', label: 'San Marino' },
    { index: 146, value: 'ST', label: 'Sao Tome and Principe' },
    { index: 147, value: 'SA', label: 'Saudi Arabia' },
    { index: 148, value: 'SN', label: 'Senegal' },
    { index: 149, value: 'RS', label: 'Serbia' },
    { index: 150, value: 'SC', label: 'Seychelles' },
    { index: 151, value: 'SL', label: 'Sierra Leone' },
    { index: 152, value: 'SG', label: 'Singapore' },
    { index: 153, value: 'SK', label: 'Slovakia' },
    { index: 154, value: 'SI', label: 'Slovenia' },
    { index: 155, value: 'SB', label: 'Solomon Islands' },
    { index: 156, value: 'SO', label: 'Somalia' },
    { index: 157, value: 'ZA', label: 'South Africa' },
    { index: 158, value: 'SS', label: 'South Sudan' },
    { index: 159, value: 'ES', label: 'Spain' },
    { index: 160, value: 'LK', label: 'Sri Lanka' },
    { index: 161, value: 'SD', label: 'Sudan' },
    { index: 162, value: 'SR', label: 'Suriname' },
    { index: 163, value: 'SE', label: 'Sweden' },
    { index: 164, value: 'CH', label: 'Switzerland' },
    { index: 165, value: 'SY', label: 'Syria' },
    { index: 166, value: 'TW', label: 'Taiwan' },
    { index: 167, value: 'TJ', label: 'Tajikistan' },
    { index: 168, value: 'TZ', label: 'Tanzania' },
    { index: 169, value: 'TH', label: 'Thailand' },
    { index: 170, value: 'TL', label: 'Timor-Leste' },
    { index: 171, value: 'TG', label: 'Togo' },
    { index: 172, value: 'TO', label: 'Tonga' },
    { index: 173, value: 'TT', label: 'Trinidad and Tobago' },
    { index: 174, value: 'TN', label: 'Tunisia' },
    { index: 175, value: 'TR', label: 'Turkey' },
    { index: 176, value: 'TM', label: 'Turkmenistan' },
    { index: 177, value: 'TV', label: 'Tuvalu' },
    { index: 178, value: 'UG', label: 'Uganda' },
    { index: 179, value: 'UA', label: 'Ukraine' },
    { index: 180, value: 'AE', label: 'United Arab Emirates' },
    { index: 181, value: 'GB', label: 'United Kingdom' },
    { index: 182, value: 'US', label: 'United States' },
    { index: 183, value: 'UY', label: 'Uruguay' },
    { index: 184, value: 'UZ', label: 'Uzbekistan' },
    { index: 185, value: 'VU', label: 'Vanuatu' },
    { index: 186, value: 'VE', label: 'Venezuela' },
    { index: 187, value: 'VN', label: 'Vietnam' },
    { index: 188, value: 'YE', label: 'Yemen' },
    { index: 189, value: 'ZM', label: 'Zambia' },
    { index: 190, value: 'ZW', label: 'Zimbabwe' },
];

const InnerCheckout = ({ plan, billing, userCount }) => {
    const { shortId } = useParams();
    const { workspaces } = useSelector(state => state.workspaces);
    const workspace = find.get(workspaces, { name: 'workspace', workspace: shortId });

    const dispatch = useDispatch();
    const [selectedPlan, setSelectedPlan] = useState(plan.name);
    const [billingCycle, setBillingCycle] = useState(billing);
    const [selectedCountry, setSelectedCountry] = useState('US');
    const [postalCode, setPostalCode] = useState('');
    const [pendingInvites, setPendingInvites] = useState(1);
    const [additionalSeats, setAdditionalSeats] = useState(0);
    const [currentSubscription, setCurrentSubscription] = useState(null);
    const [isLoadingSubscription, setIsLoadingSubscription] = useState(true);

    const [focusedElement, setFocusedElement] = useState(null);

    const { user } = useSelector((state) => state.user);


    // Inside your component:
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);



    const stripe = useStripe();
    const elements = useElements();

    const isAnnual = billingCycle === 'annually';
    const perUserPrice = planPricing[selectedPlan][billingCycle];

    // Calculate seats that need payment
    const currentReservedSeats = currentSubscription?.reservedSeats || 0;
    const unpaidSeats = Math.max(0, userCount - currentReservedSeats);

    const userTotal = perUserPrice * unpaidSeats; // Only charge for unpaid seats
    const inviteTotal = perUserPrice * pendingInvites;
    const additionalSeatsTotal = perUserPrice * additionalSeats;
    const total = userTotal + additionalSeatsTotal;

    useEffect(() => {
        dispatch(fetchWorkspaces());
    }, [dispatch]);

    // Fetch current subscription status to determine what to charge for
    useEffect(() => {
        const fetchSubscriptionStatus = async () => {
            if (!workspace?._id) return;

            try {
                setIsLoadingSubscription(true);
                const response = await fetch(`${config.API_URI}/api/billing/workspace/${shortId}/status`, {
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    setCurrentSubscription(data);
                }
            } catch (error) {
                console.error('Error fetching subscription status:', error);
            } finally {
                setIsLoadingSubscription(false);
            }
        };

        fetchSubscriptionStatus();
    }, [workspace, shortId]);

    const handlePlanSelect = (plan) => setSelectedPlan(plan);
    const toggleBillingCycle = () =>
        setBillingCycle((prev) => (prev === 'annually' ? 'monthly' : 'annually'));

    const handleCountrySelect = (selectedOption) => {
        console.log('Selected country:', selectedOption);
        setSelectedCountry(selectedOption.value);
    };


    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            // If no payment is required, just redirect to success
            if (total === 0) {
                // Re-fetch workspaces to update subscription data
                dispatch(fetchWorkspaces());
                navigate(`/w/${shortId}/payment-success`);
                return;
            }

            const response = await fetch(`${config.API_URI}/api/stripe/create-payment-intent`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    priceId: plan.priceId,
                    planType: plan.name,
                    userCount: unpaidSeats, // Only charge for unpaid seats
                    additionalSeats,
                    billingCycle,
                    workspaceId: workspace._id
                })
            });

            const { clientSecret } = await response.json();

            const result = await stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    card: elements.getElement(CardNumberElement),
                    billing_details: {
                        address: {
                            country: selectedCountry,
                            postal_code: postalCode,
                        },
                        email: user?.user.email,
                        name: user?.user.name,
                    },
                }
            });

            if (result.error) {
                throw new Error(result.error.message);
            }

            // Re-fetch workspaces to update subscription data
            dispatch(fetchWorkspaces());

            // Handle success
            navigate(`/w/${shortId}/payment-success`);

        } catch (error) {
            console.error('Payment failed:', error);
            // TODO: Add error handling UI
        } finally {
            setIsLoading(false);
        }
    };

    function getCSSVar(name) {
        return getComputedStyle(document.documentElement).getPropertyValue(name).trim();
    }


    const stripeStyle = {
        base: {
            marginTop: '4px',
            padding: '0 12px',
            border: 'none',
            height: '75',
            fontSize: '14px',
            fontWeight: '500',
            fontFamily: 'system-ui',
            color: getCSSVar('--white-text-color-alternative'),
            backgroundColor: 'transparent',
            width: '100%',
            '::placeholder': {
                fontSize: '14px',
            },
        }
    };


    // Add this near your other handlers
    const handleFocus = (elementType) => {
        setFocusedElement(elementType);
    };

    const handleBlur = () => {
        setFocusedElement(null);
    };



    return (
        <>
            {workspace ? (
                <>
                    <div className="checkout-header">
                        <div className="board-header">
                            <div className="board-icon"><span className="icon-text">Z</span></div>
                            <div className="board-details">
                                <div className="board-name"><span>{workspace.name}</span></div>
                                <div className="board-visibility">
                                    <span className="material-symbols-outlined">lock</span>
                                    <span>Private</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="checkout-container">
                        <form className="checkout-right" onSubmit={handleSubmit}>
                            <h3>Choose plan</h3>
                            <div className="plan-options">
                                {['Standard', 'Premium'].map((plan) => {
                                    const isSelected = selectedPlan === plan;
                                    return (
                                        <div
                                            key={plan}
                                            className={`plan ${isSelected ? 'selected' : ''}`}
                                            onClick={() => handlePlanSelect(plan)}
                                        >
                                            <div className="plan-name">{plan}</div>
                                            <div className="plan-price">
                                                ${planPricing[plan][billingCycle]} <span>USD</span>
                                            </div>
                                            <div className="plan-note">
                                                Per user per month billed {isAnnual ? 'annually' : 'monthly'} (
                                                ${planPricing[plan]['monthly']} billed monthly)
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>

                            <h4>Seat Configuration</h4>
                            <div className="seat-configuration">
                                <div className="seat-item">
                                    <label>Current Members</label>
                                    <div className="seat-count">{userCount} total</div>
                                </div>
                                {currentReservedSeats > 0 && (
                                    <div className="seat-item">
                                        <label>Already Paid Seats</label>
                                        <div className="seat-count paid">{currentReservedSeats} seats</div>
                                    </div>
                                )}
                                {unpaidSeats > 0 && (
                                    <div className="seat-item">
                                        <label>Seats Requiring Payment</label>
                                        <div className="seat-count unpaid">{unpaidSeats} seats</div>
                                    </div>
                                )}
                                <div className="seat-item">
                                    <label>
                                        Additional Seats (for future team members)
                                        <input
                                            type="number"
                                            min="0"
                                            max="50"
                                            value={additionalSeats}
                                            onChange={(e) => setAdditionalSeats(parseInt(e.target.value) || 0)}
                                            className="seat-input"
                                        />
                                    </label>
                                    <div className="seat-note">
                                        Reserve seats for team members you plan to add later
                                    </div>
                                </div>
                            </div>

                            <h4>Payment information</h4>
                            <div className="payment-form">
                                <div className="input-row">
                                    <label>
                                        Card Number
                                        <div className={`stripe-input ${focusedElement === 'cardNumber' ? 'focused' : ''}`}>
                                            <CardNumberElement
                                                options={{ style: stripeStyle }}
                                                onFocus={() => handleFocus('cardNumber')}
                                                onBlur={handleBlur}
                                            />
                                        </div>
                                    </label>
                                </div>
                                <div className="input-row">
                                    <label>
                                        Expiration Date
                                        <div className={`stripe-input ${focusedElement === 'cardExpiry' ? 'focused' : ''}`}>
                                            <CardExpiryElement
                                                options={{ style: stripeStyle }}
                                                onFocus={() => handleFocus('cardExpiry')}
                                                onBlur={handleBlur}
                                            />
                                        </div>
                                    </label>
                                    <label>
                                        CVC
                                        <div className={`stripe-input ${focusedElement === 'cardCvc' ? 'focused' : ''}`}>
                                            <CardCvcElement
                                                options={{ style: stripeStyle }}
                                                onFocus={() => handleFocus('cardCvc')}
                                                onBlur={handleBlur}
                                            />
                                        </div>
                                    </label>
                                </div>


                                <div className="input-row">
                                    <label>
                                        Country
                                        <SearchSelect
                                            options={countryOptions}
                                            onSelect={handleCountrySelect}
                                            placeholder="Select country"
                                            isGrouped={false}
                                            defaultValue={'US'}
                                        />
                                    </label>
                                    <label>
                                        ZIP/Postal Code
                                        <input
                                            type="text"
                                            placeholder="90210"
                                            onChange={(e) => setPostalCode(e.target.value)}
                                            className="postal-code-input"
                                        />
                                    </label>
                                </div>
                            </div>

                            <div className="billing-summary">
                                <h4>Billing summary</h4>
                                {unpaidSeats > 0 && (
                                    <div className="summary-item">
                                        <span>{unpaidSeats} New seat{unpaidSeats > 1 ? 's' : ''} ({isAnnual ? 'annual' : 'monthly'})</span>
                                        <span>${userTotal.toFixed(2)} USD</span>
                                    </div>
                                )}
                                {additionalSeats > 0 && (
                                    <div className="summary-item">
                                        <span>{additionalSeats} Additional seat{additionalSeats > 1 ? 's' : ''}</span>
                                        <span>${additionalSeatsTotal.toFixed(2)} USD</span>
                                    </div>
                                )}
                                {currentReservedSeats > 0 && (
                                    <div className="summary-note">
                                        <span>{currentReservedSeats} seat{currentReservedSeats > 1 ? 's' : ''} already paid - no additional charge</span>
                                    </div>
                                )}
                                {total > 0 ? (
                                    <div className="summary-total">
                                        <span>Total</span>
                                        <span>${total.toFixed(2)} USD</span>
                                    </div>
                                ) : (
                                    <div className="summary-total">
                                        <span>No additional payment required</span>
                                        <span>$0.00 USD</span>
                                    </div>
                                )}
                                <div className="billing-toggle">
                                    <span>Billing Cycle:</span>
                                    <span className="toggle" onClick={toggleBillingCycle}>
                                        <span className={`option ${!isAnnual ? 'active' : ''}`}>Monthly</span>
                                        <span className={`switch ${isAnnual ? 'on' : 'off'}`}></span>
                                        <span className={`option ${isAnnual ? 'active' : ''}`}>Annually</span>
                                    </span>
                                </div>
                            </div>

                            {pendingInvites > 0 && (
                                <div className="pending-invite">
                                    <p>
                                        There is currently {pendingInvites} pending invitation{pendingInvites > 1 ? 's' : ''}.
                                        You’ll be billed <strong>${inviteTotal.toFixed(2)} USD</strong> immediately for each member that accepts.
                                    </p>
                                </div>
                            )}

                            <button
                                type="submit"
                                className="confirm-btn"
                                disabled={isLoading || total === 0}
                            >
                                {isLoading ? 'Processing...' : total > 0 ? 'Confirm payment' : 'No payment required'}
                            </button>
                        </form>

                        <div className="checkout-left">
                            <h2>Zoobbe Premium is for teams who get things done.</h2>
                            <p className="feature-intro">Here are a few of your productivity boosting features:</p>
                            <ul className="features-list">
                                {plan.features.map((feature, idx) => (
                                    <li key={idx}>
                                        <span className="check">
                                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#a855f7">
                                                <path d="M382-240 154-468l57-57 171 171 367-367 57 57-424 424Z" />
                                            </svg>
                                        </span>
                                        <span className="feature-text">{feature}</span>
                                    </li>
                                ))}
                            </ul>
                            <a className="compare-link" href="#">Compare plans</a>
                            <div className="support-links">
                                <a href="#">Contact support</a>
                                <a href="#">Learn more</a>
                            </div>
                        </div>
                    </div>
                </>
            ) : (
                <p className="checkout-loading" style={{ textAlign: 'center', marginTop: '40px' }}>Loading...</p>
            )}
        </>
    );
};

const Checkout = (props) => (
    <Elements stripe={stripePromise}>
        <InnerCheckout {...props} />
    </Elements>
);

export default Checkout;

