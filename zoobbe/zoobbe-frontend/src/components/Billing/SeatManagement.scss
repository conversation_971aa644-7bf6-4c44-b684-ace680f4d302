.seat-management {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  &.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    
    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #a855f7;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .seat-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      color: #111827;
      font-size: 20px;
      font-weight: 600;
    }

    .plan-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .plan-type {
        background: #a855f7;
        color: white;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 14px;
        font-weight: 500;
      }

      .billing-cycle {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }

  .seat-overview {
    margin-bottom: 24px;

    .seat-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .stat-item {
        text-align: center;
        padding: 16px;
        background: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: #111827;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
      }
    }

    .access-breakdown {
      h4 {
        margin: 0 0 12px 0;
        color: #111827;
        font-size: 16px;
        font-weight: 600;
      }

      .access-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .access-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #374151;

          .access-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;

            &.full {
              background: #10b981;
            }

            &.limited {
              background: #f59e0b;
            }

            &.view-only {
              background: #ef4444;
            }
          }
        }
      }
    }
  }

  .pending-payment-section {
    margin-bottom: 24px;

    .pending-notice {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;
      background: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 8px;

      .notice-icon {
        font-size: 20px;
        flex-shrink: 0;
      }

      .notice-content {
        flex: 1;

        h4 {
          margin: 0 0 8px 0;
          color: #92400e;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #92400e;
          font-size: 14px;
          line-height: 1.5;
        }
      }

      .pay-button {
        background: #f59e0b;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        white-space: nowrap;

        &:hover:not(:disabled) {
          background: #d97706;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  .add-seats-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;

    h4 {
      margin: 0 0 8px 0;
      color: #111827;
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 0 0 16px 0;
      color: #6b7280;
      font-size: 14px;
    }

    .add-seats-controls {
      display: flex;
      align-items: end;
      gap: 16px;
      flex-wrap: wrap;

      .seat-input {
        display: flex;
        flex-direction: column;
        gap: 4px;

        label {
          font-size: 14px;
          font-weight: 500;
          color: #374151;
        }

        input {
          width: 80px;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;

          &:focus {
            outline: none;
            border-color: #a855f7;
            box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
          }
        }
      }

      .add-seats-button {
        background: #a855f7;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover:not(:disabled) {
          background: #9333ea;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  .billing-info {
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;

    .billing-cycle-info {
      margin-bottom: 12px;

      p {
        margin: 0 0 4px 0;
        font-size: 14px;
        color: #374151;
      }
    }

    .billing-note {
      p {
        margin: 0;
        font-size: 13px;
        color: #6b7280;
        line-height: 1.5;
        font-style: italic;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .seat-management {
    padding: 16px;
    margin: 16px 0;

    .seat-management-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .seat-overview .seat-stats {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px;
    }

    .pending-payment-section .pending-notice {
      flex-direction: column;
      gap: 12px;
    }

    .add-seats-section .add-seats-controls {
      flex-direction: column;
      align-items: stretch;

      .add-seats-button {
        width: 100%;
      }
    }
  }
}
