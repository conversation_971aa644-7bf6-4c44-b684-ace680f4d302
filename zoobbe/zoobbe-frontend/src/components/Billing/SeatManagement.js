import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { config } from '../../config';
import { stripePromise } from '../../utils/stripe';
import './SeatManagement.scss';

const SeatManagement = ({ workspaceId }) => {
    const [billingStatus, setBillingStatus] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [additionalSeats, setAdditionalSeats] = useState(1);
    const [isProcessing, setIsProcessing] = useState(false);
    
    const user = useSelector(state => state.user.user);

    useEffect(() => {
        fetchBillingStatus();
    }, [workspaceId]);

    const fetchBillingStatus = async () => {
        try {
            setIsLoading(true);
            const response = await fetch(`${config.API_URI}/api/billing/workspace/${workspaceId}/status`, {
                credentials: 'include'
            });
            
            if (response.ok) {
                const data = await response.json();
                setBillingStatus(data);
            }
        } catch (error) {
            console.error('Error fetching billing status:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handlePayPendingSeats = async () => {
        if (!billingStatus?.pendingSeats || billingStatus.pendingSeats <= 0) return;

        try {
            setIsProcessing(true);
            const response = await fetch(`${config.API_URI}/api/billing/workspace/${workspaceId}/pay-pending-seats`, {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const { clientSecret, amountDollars, seats } = await response.json();
                
                const stripe = await stripePromise;
                const { error } = await stripe.confirmCardPayment(clientSecret);

                if (error) {
                    console.error('Payment failed:', error);
                    alert('Payment failed: ' + error.message);
                } else {
                    alert(`Successfully paid for ${seats} seat${seats > 1 ? 's' : ''} ($${amountDollars})`);
                    fetchBillingStatus(); // Refresh status
                }
            }
        } catch (error) {
            console.error('Error processing payment:', error);
            alert('Payment processing failed');
        } finally {
            setIsProcessing(false);
        }
    };

    const handleIncreaseSeats = async () => {
        if (additionalSeats <= 0) return;

        try {
            setIsProcessing(true);
            const response = await fetch(`${config.API_URI}/api/billing/workspace/${workspaceId}/increase-seats`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({ additionalSeats })
            });

            if (response.ok) {
                const { clientSecret, amountDollars, seats } = await response.json();
                
                const stripe = await stripePromise;
                const { error } = await stripe.confirmCardPayment(clientSecret);

                if (error) {
                    console.error('Payment failed:', error);
                    alert('Payment failed: ' + error.message);
                } else {
                    alert(`Successfully purchased ${seats} additional seat${seats > 1 ? 's' : ''} ($${amountDollars})`);
                    fetchBillingStatus(); // Refresh status
                    setAdditionalSeats(1); // Reset input
                }
            }
        } catch (error) {
            console.error('Error processing payment:', error);
            alert('Payment processing failed');
        } finally {
            setIsProcessing(false);
        }
    };

    if (isLoading) {
        return (
            <div className="seat-management loading">
                <div className="loading-spinner"></div>
                <p>Loading billing information...</p>
            </div>
        );
    }

    if (!billingStatus?.isPro) {
        return null; // Don't show seat management for free plans
    }

    const { 
        totalMembers, 
        reservedSeats, 
        pendingSeats, 
        accessLevels, 
        planType, 
        billingCycle,
        billing 
    } = billingStatus;

    const nextBillingDate = new Date(billing?.nextBillingDate).toLocaleDateString();
    const daysUntilBilling = billing?.daysUntilBilling || 0;

    return (
        <div className="seat-management">
            <div className="seat-management-header">
                <h3>Seat Management</h3>
                <div className="plan-info">
                    <span className="plan-type">{planType}</span>
                    <span className="billing-cycle">({billingCycle})</span>
                </div>
            </div>

            <div className="seat-overview">
                <div className="seat-stats">
                    <div className="stat-item">
                        <div className="stat-number">{totalMembers}</div>
                        <div className="stat-label">Total Members</div>
                    </div>
                    <div className="stat-item">
                        <div className="stat-number">{reservedSeats}</div>
                        <div className="stat-label">Paid Seats</div>
                    </div>
                    <div className="stat-item">
                        <div className="stat-number">{pendingSeats}</div>
                        <div className="stat-label">Pending Payment</div>
                    </div>
                </div>

                <div className="access-breakdown">
                    <h4>Member Access Levels</h4>
                    <div className="access-stats">
                        <div className="access-item">
                            <span className="access-dot full"></span>
                            <span>Full Access: {accessLevels?.full || 0}</span>
                        </div>
                        <div className="access-item">
                            <span className="access-dot limited"></span>
                            <span>Limited Access: {accessLevels?.limited || 0}</span>
                        </div>
                        <div className="access-item">
                            <span className="access-dot view-only"></span>
                            <span>View Only: {accessLevels?.viewOnly || 0}</span>
                        </div>
                    </div>
                </div>
            </div>

            {pendingSeats > 0 && (
                <div className="pending-payment-section">
                    <div className="pending-notice">
                        <div className="notice-icon">⚠️</div>
                        <div className="notice-content">
                            <h4>Payment Required</h4>
                            <p>
                                You have {pendingSeats} member{pendingSeats > 1 ? 's' : ''} with view-only access. 
                                Pay now to grant them full access.
                            </p>
                        </div>
                        <button 
                            className="pay-button"
                            onClick={handlePayPendingSeats}
                            disabled={isProcessing}
                        >
                            {isProcessing ? 'Processing...' : `Pay for ${pendingSeats} Seat${pendingSeats > 1 ? 's' : ''}`}
                        </button>
                    </div>
                </div>
            )}

            <div className="add-seats-section">
                <h4>Add More Seats</h4>
                <p>Purchase additional seats for future team members</p>
                <div className="add-seats-controls">
                    <div className="seat-input">
                        <label>Additional Seats:</label>
                        <input 
                            type="number" 
                            min="1" 
                            max="50"
                            value={additionalSeats}
                            onChange={(e) => setAdditionalSeats(parseInt(e.target.value) || 1)}
                        />
                    </div>
                    <button 
                        className="add-seats-button"
                        onClick={handleIncreaseSeats}
                        disabled={isProcessing || additionalSeats <= 0}
                    >
                        {isProcessing ? 'Processing...' : `Purchase ${additionalSeats} Seat${additionalSeats > 1 ? 's' : ''}`}
                    </button>
                </div>
            </div>

            <div className="billing-info">
                <div className="billing-cycle-info">
                    <p><strong>Next billing date:</strong> {nextBillingDate}</p>
                    <p><strong>Days until billing:</strong> {daysUntilBilling}</p>
                </div>
                <div className="billing-note">
                    <p>
                        <strong>Note:</strong> Seat payments are prorated based on your current billing cycle. 
                        When you remove members, their seats remain reserved for reuse within the same billing period.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default SeatManagement;
